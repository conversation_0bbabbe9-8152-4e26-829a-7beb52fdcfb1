/**
 * 矩阵核心引擎
 * 🎯 核心价值：统一的矩阵处理引擎，数据驱动渲染，业务模式切换
 * 📦 功能范围：数据处理管道、渲染引擎、交互处理、性能优化
 * 🔄 架构设计：基于配置驱动的模块化架构，支持插件式业务模式
 */

import type {
  BusinessMode,
  CellData,
  CellRenderData,
  ContentMode,
  Coordinate,
  DataAvailability,
  InteractionEvent,
  MainMode,
  MatrixConfig,
  MatrixData,
  ModeHandler,
  ProcessedMatrixData
} from './MatrixTypes';

import {
  DEFAULT_COLOR_VALUES,
  getCachedCompleteData,
  getDataPointMappingValue,
  getMatrixDataByCoordinate,
  toDisplayCoordinate
} from '../data/GroupAData';

import { coordinateKey } from './MatrixTypes';

// ===== 新模式配置 =====

/**
 * 内容生成函数映射
 */
const CONTENT_GENERATORS: Record<ContentMode, (cell: CellData, matrixData?: any) => string> = {
  blank: () => '',
  index: (cell) => `${cell.x},${cell.y}`, // 显示坐标索引
  coordinate: (cell) => {
    const [displayX, displayY] = toDisplayCoordinate(cell.x, cell.y);
    return `${displayX},${displayY}`;
  },
  level: (cell, matrixData) => matrixData?.level?.toString() || '',
  mapping: (cell, matrixData) => {
    if (matrixData) {
      // 需要导入 getDataPointMappingValue 函数
      try {
        const mappingValue = getDataPointMappingValue(matrixData);
        return mappingValue.toString();
      } catch {
        return '';
      }
    }
    return '';
  },
  word: (cell, matrixData) => {
    // 优先使用单元格中的词语文本（来自词库绑定）
    if (cell.word) {
      return cell.word;
    }
    // 如果没有，尝试从矩阵数据中获取
    return matrixData?.word || '';
  },
  number: (cell, matrixData) => {
    // 显示数字值
    return cell.value?.toString() || matrixData?.value?.toString() || '';
  }
};

/**
 * 背景色生成逻辑
 */
const getBackgroundColorByMode = (mainMode: MainMode, matrixData?: any): string => {
  if (mainMode === 'default') {
    return '#ffffff'; // 默认白色
  }

  // 颜色模式：使用数据颜色
  if (matrixData?.color && typeof matrixData.color === 'string') {
    const colorValue = DEFAULT_COLOR_VALUES[matrixData.color as keyof typeof DEFAULT_COLOR_VALUES];
    return colorValue?.hex || '#ffffff';
  }

  return '#ffffff';
};

/**
 * 数据可用性检测
 */
export const checkDataAvailability = (): DataAvailability => {
  const completeData = getCachedCompleteData();

  // 检查词库数据可用性
  let hasWordData = false;
  try {
    // 动态检查词库数据
    if (typeof window !== 'undefined') {
      // 在客户端环境中检查词库数据
      const wordLibraryStore = require('@/core/wordLibrary/WordLibraryStore').useWordLibraryStore;
      const state = wordLibraryStore.getState();
      hasWordData = Array.from(state.libraries.values()).some((library: any) => library.words.length > 0);
    }
  } catch (error) {
    // 如果无法访问词库数据，默认为有数据（因为我们添加了默认数据）
    hasWordData = true;
  }

  return {
    hasCoordinateData: true, // 坐标总是可用的
    hasLevelData: completeData.points.some(point => point.level !== undefined),
    hasMappingData: completeData.points.some(point => {
      const colorValue = DEFAULT_COLOR_VALUES[point.color];
      return colorValue.mappingValue !== undefined;
    }),
    hasWordData // 基于实际词库数据检测
  };
};

// ===== 旧模式配置（保持向后兼容） =====

/**
 * 统一的模式配置 - 配置驱动设计
 * 所有模式的差异都通过配置来定义
 */
interface ModeConfig {
  /** 内容生成函数 */
  contentFn: (cell: CellData) => string;
  /** 是否使用矩阵数据颜色 */
  useMatrixColor?: boolean;
  /** 自定义字体大小 */
  fontSize?: string;
}

const MODE_CONFIGS: Record<BusinessMode, ModeConfig> = {
  coordinate: {
    contentFn: (cell: CellData) => {
      const [displayX, displayY] = toDisplayCoordinate(cell.x, cell.y);
      return `${displayX},${displayY}`;
    },
    fontSize: '9px'
  },
  color: {
    contentFn: () => '',
    useMatrixColor: true
  },
  level: {
    contentFn: (cell: CellData) => cell.level?.toString() || ''
  },
  word: {
    contentFn: (cell: CellData) => cell.word || ''
  },
  'color-word': {
    contentFn: (cell: CellData) => cell.word || '',
    useMatrixColor: true
  },
  'number-word': {
    contentFn: (cell: CellData) => cell.word || cell.level?.toString() || ''
  }
};

// ===== 统一的样式工具函数 =====

/**
 * 统一的背景色函数 - 极简化设计
 */
const getBackgroundColor = (mode: BusinessMode, cellColor?: any): string => {
  const config = MODE_CONFIGS[mode];
  if (config.useMatrixColor && cellColor && DEFAULT_COLOR_VALUES[cellColor as keyof typeof DEFAULT_COLOR_VALUES]) {
    return DEFAULT_COLOR_VALUES[cellColor as keyof typeof DEFAULT_COLOR_VALUES].hex;
  }
  return '#ffffff';
};

/**
 * 统一的文字颜色函数 - 极简化设计
 */
const getTextColor = (backgroundColor: string): string => {
  const isDarkBackground = backgroundColor === '#000000' ||
    (backgroundColor.startsWith('#') && parseInt(backgroundColor.slice(1), 16) < 0x808080);
  return isDarkBackground ? '#ffffff' : '#000000';
};

/**
 * 统一的样式创建函数 - 配置驱动
 */
const createCellStyle = (cell: CellData, mode: BusinessMode, matrixDataColor?: any) => {
  const config = MODE_CONFIGS[mode];
  const cellColor = matrixDataColor || cell.color;
  const backgroundColor = getBackgroundColor(mode, cellColor);

  return {
    backgroundColor,
    border: '1px solid #e0e0e0',
    borderRadius: '6px',
    color: getTextColor(backgroundColor),
    fontSize: config.fontSize || '16px',
    fontWeight: 'bold',
    boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
    transition: 'all 0.2s ease',
  };
};

/**
 * 统一的类名创建函数 - 极简化设计
 */
const createCellClassName = (cell: CellData, mode: BusinessMode) => {
  const classes = ['matrix-cell', `${mode}-mode`];
  if (cell.isSelected) classes.push('selected');
  if (cell.isHovered) classes.push('hovered');
  return classes.join(' ');
};

/**
 * 统一的内容创建函数 - 配置驱动
 */
const createCellContent = (cell: CellData, mode: BusinessMode) => {
  const config = MODE_CONFIGS[mode];
  return config.contentFn(cell);
};

// ===== 统一的模式处理器工厂 =====

/**
 * 创建统一的模式处理器 - 配置驱动设计
 * 消除重复代码，所有模式使用相同的处理逻辑
 */
const createModeHandler = (mode: BusinessMode): ModeHandler => {
  const config = MODE_CONFIGS[mode];

  return {
    processData: (data: MatrixData): ProcessedMatrixData => {
      const renderData = new Map<string, CellRenderData>();

      data.cells.forEach((cell, key) => {
        // 获取矩阵数据颜色（仅颜色模式需要）
        let cellColor = cell.color;
        if (config.useMatrixColor) {
          const completeData = getCachedCompleteData();
          const matrixData = getMatrixDataByCoordinate(completeData, cell.x, cell.y);
          cellColor = matrixData?.color || cell.color;
        }

        renderData.set(key, {
          content: createCellContent(cell, mode),
          style: createCellStyle(cell, mode, cellColor),
          className: createCellClassName(cell, mode),
          isInteractive: true,
        });
      });

      return {
        cells: data.cells,
        renderData,
        metadata: {
          totalCells: data.cells.size,
          activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
          selectedCells: data.selectedCells.size,
          mode,
        },
      };
    },

    renderCell: (cell: CellData): CellRenderData => {
      // 获取矩阵数据颜色（仅颜色模式需要）
      let cellColor = cell.color;
      if (config.useMatrixColor) {
        const completeData = getCachedCompleteData();
        const matrixData = getMatrixDataByCoordinate(completeData, cell.x, cell.y);
        cellColor = matrixData?.color || cell.color;
      }

      return {
        content: createCellContent(cell, mode),
        style: createCellStyle(cell, mode, cellColor),
        className: createCellClassName(cell, mode),
        isInteractive: true,
      };
    },

    handleInteraction: (event: InteractionEvent, cell: CellData) => {
      console.log(`${mode} mode interaction: ${event.type} at (${cell.x}, ${cell.y})`);
    },
  };
};

// ===== 模式处理器注册表 =====

const modeHandlers: Record<BusinessMode, ModeHandler> = {
  coordinate: createModeHandler('coordinate'),
  color: createModeHandler('color'),
  level: createModeHandler('level'),
  word: createModeHandler('word'),
  'color-word': createModeHandler('color-word'),
  'number-word': createModeHandler('number-word'),
};

// ===== 矩阵核心引擎 =====

export class MatrixCore {
  /**
   * 处理矩阵数据 - 简化版本
   */
  processData(data: MatrixData, config: MatrixConfig): ProcessedMatrixData {
    // 如果有新模式配置，使用新的处理方式
    if (config.mainMode && config.contentMode) {
      return processMatrixDataByMode(data, config.mainMode, config.contentMode);
    }

    // 否则使用旧的处理方式（向后兼容）
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    return handler.processData(data);
  }

  /**
   * 渲染单个单元格 - 简化版本
   */
  renderCell(cell: CellData, config: MatrixConfig): CellRenderData {
    // 如果有新模式配置，使用新的渲染方式
    if (config.mainMode && config.contentMode) {
      return renderCellByMode(cell, config.mainMode, config.contentMode);
    }

    // 否则使用旧的渲染方式（向后兼容）
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    return handler.renderCell(cell);
  }

  /**
   * 处理交互事件 - 简化版本
   */
  handleInteraction(event: InteractionEvent, cell: CellData, config: MatrixConfig): void {
    const handler = modeHandlers[config.mode];
    if (!handler) {
      throw new Error(`Unknown business mode: ${config.mode}`);
    }

    handler.handleInteraction(event, cell);
  }

  /**
   * 切换业务模式 - 简化版本
   */
  switchMode(mode: BusinessMode, data: MatrixData, config: MatrixConfig): ProcessedMatrixData {
    const newConfig = { ...config, mode };
    return this.processData(data, newConfig);
  }

  /**
   * 批量更新单元格 - 简化版本
   */
  batchUpdateCells(
    updates: Array<{ coordinate: Coordinate; data: Partial<CellData> }>,
    data: MatrixData
  ): MatrixData {
    const newData = { ...data };
    const newCells = new Map(data.cells);

    updates.forEach(({ coordinate, data: cellData }) => {
      const key = coordinateKey(coordinate.x, coordinate.y);
      const existingCell = newCells.get(key);

      if (existingCell) {
        newCells.set(key, { ...existingCell, ...cellData });
      }
    });

    newData.cells = newCells;
    return newData;
  }

  /**
   * 验证矩阵数据
   */
  validateData(data: MatrixData): boolean {
    // 检查数据完整性
    if (!data.cells || !(data.cells instanceof Map)) {
      return false;
    }

    // 检查单元格数据
    for (const [key, cell] of data.cells) {
      if (!this.validateCell(cell)) {
        console.warn(`Invalid cell data at ${key}:`, cell);
        return false;
      }
    }

    return true;
  }

  /**
   * 验证单元格数据
   */
  private validateCell(cell: CellData): boolean {
    return (
      typeof cell.x === 'number' &&
      typeof cell.y === 'number' &&
      cell.x >= 0 && cell.x < 33 &&
      cell.y >= 0 && cell.y < 33 &&
      typeof cell.isActive === 'boolean' &&
      typeof cell.isSelected === 'boolean' &&
      typeof cell.isHovered === 'boolean'
    );
  }
}

// ===== 单例实例 =====

export const matrixCore = new MatrixCore();

// ===== 工具函数 =====

/**
 * 创建交互事件
 */
export const createInteractionEvent = (
  type: InteractionEvent['type'],
  coordinate: Coordinate,
  modifiers: Partial<InteractionEvent['modifiers']> = {},
  data?: any
): InteractionEvent => ({
  type,
  coordinate,
  modifiers: {
    ctrl: false,
    shift: false,
    alt: false,
    ...modifiers,
  },
  data,
});

/**
 * 注册自定义模式处理器
 */
export const registerModeHandler = (mode: string, handler: ModeHandler): void => {
  (modeHandlers as any)[mode] = handler;
};

/**
 * 获取可用的业务模式
 */
export const getAvailableModes = (): BusinessMode[] => {
  return Object.keys(modeHandlers) as BusinessMode[];
};

// ===== 新模式处理函数 =====

/**
 * 根据新模式配置渲染单元格
 */
export const renderCellByMode = (
  cell: CellData,
  mainMode: MainMode,
  contentMode: ContentMode
): CellRenderData => {
  // 获取矩阵数据
  const completeData = getCachedCompleteData();
  const matrixData = getMatrixDataByCoordinate(completeData, cell.x, cell.y);

  // 生成内容
  const content = CONTENT_GENERATORS[contentMode](cell, matrixData);

  // 生成背景色
  const backgroundColor = getBackgroundColorByMode(mainMode, matrixData);

  // 生成文本颜色
  const textColor = getTextColor(backgroundColor);

  return {
    content,
    style: {
      backgroundColor,
      color: textColor,
      border: '1px solid #e0e0e0',
      borderRadius: '6px',
      fontSize: (contentMode === 'coordinate' || contentMode === 'index') ? '9px' : contentMode === 'word' ? '14px' : '16px',
      fontWeight: 'bold',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      transition: 'all 0.2s ease',
    },
    className: `matrix-cell ${mainMode}-mode ${contentMode}-content`,
    isInteractive: true,
  };
};

/**
 * 处理矩阵数据（新模式）
 */
export const processMatrixDataByMode = (
  data: MatrixData,
  mainMode: MainMode,
  contentMode: ContentMode
): ProcessedMatrixData => {
  const renderData = new Map<string, CellRenderData>();

  data.cells.forEach((cell, key) => {
    renderData.set(key, renderCellByMode(cell, mainMode, contentMode));
  });

  return {
    cells: data.cells,
    renderData,
    metadata: {
      totalCells: data.cells.size,
      activeCells: Array.from(data.cells.values()).filter(cell => cell.isActive).length,
      selectedCells: data.selectedCells.size,
      mode: 'coordinate', // 保持向后兼容
    },
  };
};
