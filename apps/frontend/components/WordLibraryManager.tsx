/**
 * 词库管理主组件
 * 🎯 核心价值：统一的词库管理界面，支持29个词库的可视化管理
 * 📦 功能范围：词库列表、折叠展开、颜色分类、词语管理
 * 🔄 架构设计：基于状态驱动的响应式组件，支持实时更新
 */

'use client';

import Button from '@/components/ui/Button';
import CombinedWordLibraryInput from '@/components/ui/CombinedWordLibraryInput';
import type {
  BasicColorType,
  DataLevel,
  WordLibraryKey
} from '@/core/matrix/MatrixTypes';
import {
  AVAILABLE_WORD_LIBRARIES,
  getWordLibraryBackgroundColor,
  getWordLibraryDisplayName
} from '@/core/wordLibrary/WordLibraryCore';
import { useWordInputStore, useWordLibraryStore } from '@/core/wordLibrary/WordLibraryStore';
import React, { memo, useCallback, useEffect, useMemo, useRef } from 'react';

// ===== 组件属性 =====

interface WordLibraryManagerProps {
  /** 自定义类名 */
  className?: string;
  /** 自定义样式 */
  style?: React.CSSProperties;
  /** 是否为颜色词语模式（可选，用于外部控制） */
  isColorWordMode?: boolean;
}

// ===== 词库项组件 =====

// ===== 词库项组件 =====

interface WordLibraryItemProps {
  color: BasicColorType;
  level: DataLevel;
  libraryKey: WordLibraryKey;
}

const WordLibraryItem: React.FC<WordLibraryItemProps> = ({ color, level, libraryKey }) => {
  const itemRef = useRef<HTMLDivElement>(null);
  const {
    getLibrary
  } = useWordLibraryStore();

  // 获取填词模式状态
  const { isActive: isWordInputActive, matchedLibrary } = useWordInputStore();

  const library = getLibrary(libraryKey);
  const displayName = getWordLibraryDisplayName(color, level);
  const backgroundColor = getWordLibraryBackgroundColor(color);

  // 检查是否为当前使用的词库 - 使用useMemo避免频繁重计算
  const isActiveLibrary = useMemo(() => {
    return isWordInputActive && matchedLibrary === libraryKey;
  }, [isWordInputActive, matchedLibrary, libraryKey]);



  // 使用ref来跟踪是否已经为当前激活状态滑动过
  const hasScrolledForCurrentActivation = useRef(false);
  const lastActiveLibraryRef = useRef<string | null>(null);
  const activationTimestampRef = useRef<number>(0);

  // 检查元素是否在可视区域内
  const isElementInViewport = useCallback((element: HTMLElement): boolean => {
    const rect = element.getBoundingClientRect();
    const container = element.closest('.overflow-y-auto');

    if (container) {
      const containerRect = container.getBoundingClientRect();
      return (
        rect.top >= containerRect.top &&
        rect.bottom <= containerRect.bottom
      );
    }

    // 如果没有找到滚动容器，检查是否在窗口可视区域内
    return (
      rect.top >= 0 &&
      rect.bottom <= window.innerHeight
    );
  }, []);

  // 滑动到可视区域中间 - 确保只在双击激活时触发一次
  useEffect(() => {
    // 如果不是激活状态，重置滑动标记
    if (!isActiveLibrary) {
      hasScrolledForCurrentActivation.current = false;
      lastActiveLibraryRef.current = null;
      activationTimestampRef.current = 0;
      return;
    }

    // 如果没有元素引用，跳过
    if (!itemRef.current) {
      return;
    }

    // 检查是否是新的激活状态（不同的词库被激活）
    const currentLibraryKey = matchedLibrary;
    const isNewActivation = lastActiveLibraryRef.current !== currentLibraryKey;

    // 如果不是新的激活状态且已经滑动过，跳过
    if (!isNewActivation && hasScrolledForCurrentActivation.current) {
      return;
    }

    // 如果是新的激活状态，记录时间戳并立即执行滑动
    if (isNewActivation) {
      activationTimestampRef.current = Date.now();

      // 检查是否已经在可视区域内
      if (isElementInViewport(itemRef.current)) {
        // 词库已在可视区域内，无需滑动
        hasScrolledForCurrentActivation.current = true;
        lastActiveLibraryRef.current = currentLibraryKey;
        return;
      }

      // 只有当元素不在可视区域时才滑动
      itemRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'center'
      });

      // 标记已滑动
      hasScrolledForCurrentActivation.current = true;
      lastActiveLibraryRef.current = currentLibraryKey;
    }
  }, [isActiveLibrary, matchedLibrary, libraryKey]);

  if (!library) return null;

  return (
    <div ref={itemRef} className="mb-3" data-word-library={libraryKey}>
      {/* 合并式词库输入组件 */}
      <CombinedWordLibraryInput
        libraryKey={libraryKey}
        color={color}
        level={level}
        collapsed={library.collapsed}
        placeholder={`输入${displayName}词语...`}
        className={isActiveLibrary ? 'word-library-active' : ''}
      />
    </div>
  );
};



// ===== 主组件 =====

const WordLibraryManagerComponent: React.FC<WordLibraryManagerProps> = ({
  className = '',
  style,
  isColorWordMode = true // 默认为true，保持向后兼容
}) => {
  const { resetAllLibraries, exportData, importData } = useWordLibraryStore();

  // 如果不是【颜色】【词语】模式，显示提示
  if (!isColorWordMode) {
    return (
      <div className={`word-library-manager ${className} flex flex-col h-full items-center justify-center`} style={style}>
        <div className="text-center text-gray-500">
          <p className="text-sm">词库管理功能仅在【颜色】【词语】模式下可用</p>
          <p className="text-xs mt-1">请切换到颜色模式 + 词语内容模式</p>
        </div>
      </div>
    );
  }

  // 处理导出
  const handleExport = useCallback(() => {
    const data = exportData();
    const blob = new Blob([data], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `词库数据_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [exportData]);

  // 处理导入
  const handleImport = useCallback(() => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        const reader = new FileReader();
        reader.onload = (e) => {
          const data = e.target?.result as string;
          const success = importData(data);
          if (success) {
            alert('导入成功！');
          } else {
            alert('导入失败，请检查文件格式。');
          }
        };
        reader.readAsText(file);
      }
    };
    input.click();
  }, [importData]);

  // 处理重置
  const handleReset = useCallback(() => {
    if (confirm('确定要清空所有词库吗？此操作不可撤销。')) {
      resetAllLibraries();
    }
  }, [resetAllLibraries]);

  return (
    <div className={`word-library-manager ${className} flex flex-col h-full`} style={style}>
      {/* 标题栏 */}
      <div className="flex items-center justify-between mb-4 flex-shrink-0">
        <h3 className="text-lg font-semibold text-gray-800">词库管理</h3>
        <div className="flex space-x-2">
          <Button
            variant="secondary"
            size="sm"
            onClick={handleExport}
            title="导出词库数据"
          >
            导出
          </Button>
          <Button
            variant="secondary"
            size="sm"
            onClick={handleImport}
            title="导入词库数据"
          >
            导入
          </Button>
          <Button
            variant="danger"
            size="sm"
            onClick={handleReset}
            title="清空所有词库"
          >
            清空
          </Button>
        </div>
      </div>

      {/* 词库列表 - 可滚动容器 */}
      <div className="flex-1 overflow-y-auto space-y-1 pr-2">
        {AVAILABLE_WORD_LIBRARIES.map(({ color, level }) => {
          const libraryKey = `${color}-${level}` as WordLibraryKey;
          return (
            <WordLibraryItem
              key={libraryKey}
              color={color}
              level={level}
              libraryKey={libraryKey}
            />
          );
        })}
      </div>
    </div>
  );
};

// ===== 性能优化 =====

const WordLibraryManager = memo(WordLibraryManagerComponent);

WordLibraryManager.displayName = 'WordLibraryManager';

export default WordLibraryManager;
export type { WordLibraryManagerProps };
